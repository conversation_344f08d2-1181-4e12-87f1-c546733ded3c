body {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial;
}

.main-container {
    border: 1px solid #ccc;
    padding: 15px;
    margin-bottom: 15px;
}

h1 {
    text-align: center;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin: 15px 0;
}

th, td {
    border: 1px solid #ccc;
    padding: 8px;
}

th {
    background: #f0f0f0;
}

.order-info {
    background: #f8f8f8;
    padding: 10px;
    margin: 10px 0;
}

button {
    background: #0066cc;
    color: white;
    border: none;
    padding: 8px 15px;
    cursor: pointer;
}

select {
    padding: 5px;
}

#status-message {
    display: none;
    padding: 10px;
    margin: 10px 0;
}

#status-message.success {
    background: #e8f5e8;
    color: green;
}

#status-message.error {
    background: #ffe8e8;
    color: red;
}

/* Shipping label styles */
.shipping-label-container {
    border: 2px solid #333;
    padding: 12px;
    background: #fff;
    font-family: Arial, sans-serif;
    font-size: 11px;
    width: 280px;
    height: 180px;
}

.label-header {
    text-align: center;
    font-weight: bold;
    font-size: 13px;
    margin-bottom: 12px;
    text-transform: uppercase;
}

.label-info {
    margin-bottom: 10px;
}

.label-address {
    margin-bottom: 10px;
}

.barcode-area {
    text-align: center;
    margin-top: 12px;
    padding: 8px;
    background: #f5f5f5;
    border: 1px solid #ddd;
}

.barcode-display {
    font-family: monospace;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 2px;
}

.tracking-code {
    font-size: 9px;
    margin-top: 4px;
}

/* PDF document styles */
.document-header {
    margin-bottom: 20px;
}

.address-container {
    display: flex;
    margin-bottom: 20px;
}

.addr-left, .addr-right {
    flex: 1;
}

.addr-right {
    margin-left: 20px;
}

.address-box {
    border: 1px solid #ccc;
    padding: 10px;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.items-table th, .items-table td {
    border: 1px solid #ccc;
    padding: 8px;
    text-align: left;
}

.items-table th {
    background: #f0f0f0;
}

.shipping-label {
    margin-top: 20px;
}